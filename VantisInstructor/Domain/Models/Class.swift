//
//  Class.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation

// MARK: - Class Model
struct Class: Codable, Identifiable {
    let id: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let classNumber: Int
    let title: String
    let description: String?
    let type: ClassType
    let status: ClassStatus
    let scheduledDate: Date
    let startTime: Date
    let endTime: Date
    let duration: Int // minutes
    let location: ClassLocation?
    let instructorId: String
    let instructorName: String
    let totalStudents: Int
    let attendedStudents: Int
    let absentStudents: Int
    let lateStudents: Int
    let materials: [ClassMaterial]?
    let assignments: [String]? // Assignment IDs
    let announcements: [String]? // Announcement IDs
    let recordingUrl: String?
    let notes: String?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var attendanceRate: Double {
        guard totalStudents > 0 else { return 0 }
        return Double(attendedStudents) / Double(totalStudents) * 100
    }
    
    var isToday: Bool {
        Calendar.current.isDateInToday(scheduledDate)
    }
    
    var isUpcoming: Bool {
        scheduledDate > Date()
    }
    
    var isPast: Bool {
        scheduledDate < Date()
    }
    
    var timeRemaining: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        
        if isUpcoming {
            return formatter.string(from: Date(), to: scheduledDate) ?? ""
        }
        return ""
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return "\(formatter.string(from: startTime)) - \(formatter.string(from: endTime))"
    }
}

// MARK: - Class Type
enum ClassType: String, Codable, CaseIterable {
    case lecture = "LECTURE"
    case lab = "LAB"
    case tutorial = "TUTORIAL"
    case seminar = "SEMINAR"
    case workshop = "WORKSHOP"
    case exam = "EXAM"
    case review = "REVIEW"
    case presentation = "PRESENTATION"
    
    var displayName: String {
        switch self {
        case .lecture: return "Bài giảng"
        case .lab: return "Thực hành"
        case .tutorial: return "Hướng dẫn"
        case .seminar: return "Hội thảo"
        case .workshop: return "Workshop"
        case .exam: return "Kiểm tra"
        case .review: return "Ôn tập"
        case .presentation: return "Thuyết trình"
        }
    }
    
    var icon: String {
        switch self {
        case .lecture: return "book"
        case .lab: return "flask"
        case .tutorial: return "questionmark.circle"
        case .seminar: return "person.3"
        case .workshop: return "hammer"
        case .exam: return "doc.text"
        case .review: return "arrow.clockwise"
        case .presentation: return "presentation"
        }
    }
    
    var color: String {
        switch self {
        case .lecture: return "blue"
        case .lab: return "green"
        case .tutorial: return "orange"
        case .seminar: return "purple"
        case .workshop: return "red"
        case .exam: return "pink"
        case .review: return "yellow"
        case .presentation: return "indigo"
        }
    }
}

// MARK: - Class Status
enum ClassStatus: String, Codable, CaseIterable {
    case scheduled = "SCHEDULED"
    case inProgress = "IN_PROGRESS"
    case completed = "COMPLETED"
    case cancelled = "CANCELLED"
    case postponed = "POSTPONED"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Đã lên lịch"
        case .inProgress: return "Đang diễn ra"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .postponed: return "Hoãn lại"
        }
    }
    
    var color: String {
        switch self {
        case .scheduled: return "blue"
        case .inProgress: return "green"
        case .completed: return "gray"
        case .cancelled: return "red"
        case .postponed: return "orange"
        }
    }

    // Convert to APILessonState for compatibility
    func toAPILessonState() -> APILessonState {
        switch self {
        case .scheduled: return .scheduled
        case .inProgress: return .inProgress
        case .completed: return .completed
        case .cancelled: return .cancelled
        case .postponed: return .scheduled // Map postponed to scheduled
        }
    }
}

// MARK: - Class Location
struct ClassLocation: Codable {
    let building: String
    let room: String
    let floor: Int?
    let capacity: Int?
    let equipment: [String]?
    let address: String?
    
    var displayName: String {
        return "\(building) - \(room)"
    }
}

// MARK: - Class Material
struct ClassMaterial: Codable, Identifiable {
    let id: String
    let title: String
    let type: MaterialType
    let url: String?
    let fileSize: Int? // bytes
    let description: String?
    let isRequired: Bool
    let uploadedAt: Date
}

// MARK: - Material Type
enum MaterialType: String, Codable, CaseIterable {
    case slides = "SLIDES"
    case document = "DOCUMENT"
    case video = "VIDEO"
    case audio = "AUDIO"
    case link = "LINK"
    case code = "CODE"
    case dataset = "DATASET"
    
    var displayName: String {
        switch self {
        case .slides: return "Slide"
        case .document: return "Tài liệu"
        case .video: return "Video"
        case .audio: return "Audio"
        case .link: return "Liên kết"
        case .code: return "Code"
        case .dataset: return "Dữ liệu"
        }
    }
    
    var icon: String {
        switch self {
        case .slides: return "rectangle.stack"
        case .document: return "doc"
        case .video: return "video"
        case .audio: return "speaker.wave.2"
        case .link: return "link"
        case .code: return "chevron.left.forwardslash.chevron.right"
        case .dataset: return "chart.bar"
        }
    }
}

// MARK: - Class Extensions
extension Class {
    // Convert to APILesson for compatibility with LessonDetailBottomSheet
    func toAPILesson() -> APILesson {
        return APILesson(
            id: Int(id) ?? 0,
            name: title,
            lessonNumber: classNumber,
            state: status.toAPILessonState(),
            className: courseName,
            classCode: courseCode,
            startDatetime: ISO8601DateFormatter().string(from: startTime),
            durationHours: Double(duration) / 60.0, // Convert minutes to hours
            room: location?.room ?? "TBD",
            totalStudents: totalStudents,
            presentCount: attendedStudents,
            attendanceRate: totalStudents > 0 ? Double(attendedStudents) / Double(totalStudents) * 100 : 0,
            isToday: Calendar.current.isDateInToday(scheduledDate),
            isUpcoming: scheduledDate > Date(),
            isOverdue: scheduledDate < Date() && status == .scheduled,
            needsAttention: status == .cancelled || (scheduledDate < Date() && status == .scheduled)
        )
    }

    static let mockClasses: [Class] = [
        Class(
            id: "class_1",
            courseId: "course_1",
            courseName: "Lập trình iOS với Swift",
            courseCode: "CS301",
            classNumber: 16,
            title: "SwiftUI Navigation và State Management",
            description: "Học cách sử dụng NavigationView, NavigationLink và quản lý state trong SwiftUI",
            type: .lecture,
            status: .scheduled,
            scheduledDate: Date().addingTimeInterval(2 * 3600), // 2 hours from now
            startTime: Date().addingTimeInterval(2 * 3600),
            endTime: Date().addingTimeInterval(4 * 3600),
            duration: 120,
            location: ClassLocation(
                building: "Tòa A",
                room: "A301",
                floor: 3,
                capacity: 40,
                equipment: ["Projector", "Whiteboard", "Computer"],
                address: "123 Đường ABC, Quận 1, TP.HCM"
            ),
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            totalStudents: 35,
            attendedStudents: 0,
            absentStudents: 0,
            lateStudents: 0,
            materials: [
                ClassMaterial(
                    id: "material_1",
                    title: "SwiftUI Navigation - Slides",
                    type: .slides,
                    url: "https://example.com/slides.pdf",
                    fileSize: 2048000,
                    description: "Slide bài giảng về Navigation trong SwiftUI",
                    isRequired: true,
                    uploadedAt: Date().addingTimeInterval(-3600)
                )
            ],
            assignments: ["assignment_1"],
            announcements: nil,
            recordingUrl: nil,
            notes: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-24 * 3600),
            updatedAt: Date()
        )
    ]
}
