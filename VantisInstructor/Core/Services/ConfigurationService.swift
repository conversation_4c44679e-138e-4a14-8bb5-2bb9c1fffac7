//
//  ConfigurationService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import Combine
import UIKit

// MARK: - Configuration Service
class ConfigurationService: ObservableObject {
    static let shared = ConfigurationService()
    
    private let apiClient = APIClient.shared
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()
    
    @Published var appConfig: AppConfiguration?
    @Published var featureFlags: FeatureFlags?
    @Published var maintenanceMode: MaintenanceInfo?
    @Published var isLoading = false
    
    // Cache configuration for offline use
    private let configCacheKey = "app_configuration_cache"
    private let featureFlagsCacheKey = "feature_flags_cache"
    
    private init() {
        loadCachedConfiguration()
        setupPeriodicRefresh()
    }
    
    // MARK: - Configuration Loading
    func loadConfiguration() async {
        await MainActor.run {
            isLoading = true
        }

        // For now, use default configuration since backend endpoints are not ready
        logger.info("Using default configuration (backend endpoints not implemented yet)", category: .general)

        // Use cached configuration if available, otherwise use defaults
        if appConfig == nil {
            loadCachedConfiguration()

            // If still no cached config, use defaults
            if appConfig == nil {
                await MainActor.run {
                    self.appConfig = AppConfiguration.defaultConfig
                    self.featureFlags = FeatureFlags.defaultFlags
                }
            }
        }

        await MainActor.run {
            isLoading = false
        }
    }
    
    private func loadAppConfiguration() async throws {
        let response: APIResponse<AppConfiguration> = try await apiClient.request(
            endpoint: APIEndpoints.System.config,
            method: .GET,
            responseType: APIResponse<AppConfiguration>.self,
            requiresAuth: false
        )
        
        if let config = response.data {
            await MainActor.run {
                self.appConfig = config
            }
            
            // Cache configuration
            cacheConfiguration(config)
        }
    }
    
    private func loadFeatureFlags() async throws {
        let response: APIResponse<FeatureFlags> = try await apiClient.request(
            endpoint: "/system/feature-flags",
            method: .GET,
            responseType: APIResponse<FeatureFlags>.self,
            requiresAuth: false
        )
        
        if let flags = response.data {
            await MainActor.run {
                self.featureFlags = flags
            }
            
            // Cache feature flags
            cacheFeatureFlags(flags)
        }
    }
    
    private func checkMaintenanceStatus() async throws {
        let response: APIResponse<MaintenanceInfo> = try await apiClient.request(
            endpoint: APIEndpoints.System.maintenance,
            method: .GET,
            responseType: APIResponse<MaintenanceInfo>.self,
            requiresAuth: false
        )
        
        if let maintenance = response.data {
            await MainActor.run {
                self.maintenanceMode = maintenance.isActive ? maintenance : nil
            }
        }
    }
    
    // MARK: - Feature Flag Checking
    func isFeatureEnabled(_ feature: String) -> Bool {
        return featureFlags?.isEnabled(feature) ?? AppConstants.FeatureFlags.isEnabled(feature)
    }
    
    func getFeatureConfig<T>(_ feature: String, type: T.Type) -> T? {
        return featureFlags?.getConfig(feature, type: type)
    }
    
    // MARK: - Configuration Values
    func getConfigValue<T>(_ key: String, type: T.Type, defaultValue: T) -> T {
        return appConfig?.getValue(key, type: type) ?? defaultValue
    }
    
    func getAPIEndpoint(_ endpoint: String) -> String {
        let baseURL = appConfig?.apiBaseURL ?? AppConstants.Environment.current.apiBaseURL
        return baseURL + endpoint
    }
    
    // MARK: - Caching
    private func cacheConfiguration(_ config: AppConfiguration) {
        do {
            let data = try JSONEncoder().encode(config)
            UserDefaults.standard.set(data, forKey: configCacheKey)
        } catch {
            logger.error("Failed to cache configuration", error: error, category: .general)
        }
    }
    
    private func cacheFeatureFlags(_ flags: FeatureFlags) {
        do {
            let data = try JSONEncoder().encode(flags)
            UserDefaults.standard.set(data, forKey: featureFlagsCacheKey)
        } catch {
            logger.error("Failed to cache feature flags", error: error, category: .general)
        }
    }
    
    private func loadCachedConfiguration() {
        // Load cached app configuration
        if let data = UserDefaults.standard.data(forKey: configCacheKey) {
            do {
                appConfig = try JSONDecoder().decode(AppConfiguration.self, from: data)
            } catch {
                logger.error("Failed to decode cached configuration", error: error, category: .general)
            }
        }
        
        // Load cached feature flags
        if let data = UserDefaults.standard.data(forKey: featureFlagsCacheKey) {
            do {
                featureFlags = try JSONDecoder().decode(FeatureFlags.self, from: data)
            } catch {
                logger.error("Failed to decode cached feature flags", error: error, category: .general)
            }
        }
    }
    
    // MARK: - Periodic Refresh
    private func setupPeriodicRefresh() {
        // Refresh configuration every 30 minutes
        Timer.scheduledTimer(withTimeInterval: 1800, repeats: true) { [weak self] _ in
            Task {
                await self?.loadConfiguration()
            }
        }
        
        // Refresh when app becomes active
        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                Task {
                    await self?.loadConfiguration()
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - App Configuration Model
struct AppConfiguration: Codable {
    let apiBaseURL: String
    let socketURL: String
    let cdnBaseURL: String
    let supportEmail: String
    let supportPhone: String
    let minAppVersion: String
    let forceUpdateVersion: String?
    let exchangeRate: Double
    let transactionFee: Double
    let maxTransferAmount: Double
    let minTransferAmount: Double
    let sessionTimeout: TimeInterval
    let cacheTimeout: TimeInterval
    let retryAttempts: Int
    let retryDelay: TimeInterval
    
    enum CodingKeys: String, CodingKey {
        case apiBaseURL = "api_base_url"
        case socketURL = "socket_url"
        case cdnBaseURL = "cdn_base_url"
        case supportEmail = "support_email"
        case supportPhone = "support_phone"
        case minAppVersion = "min_app_version"
        case forceUpdateVersion = "force_update_version"
        case exchangeRate = "exchange_rate"
        case transactionFee = "transaction_fee"
        case maxTransferAmount = "max_transfer_amount"
        case minTransferAmount = "min_transfer_amount"
        case sessionTimeout = "session_timeout"
        case cacheTimeout = "cache_timeout"
        case retryAttempts = "retry_attempts"
        case retryDelay = "retry_delay"
    }
    
    func getValue<T>(_ key: String, type: T.Type) -> T? {
        return nil // Features removed for simplicity
    }
    
    var isUpdateRequired: Bool {
        let currentVersion = AppConstants.AppInfo.version
        return currentVersion.compare(minAppVersion, options: .numeric) == .orderedAscending
    }
    
    var isForceUpdateRequired: Bool {
        guard let forceVersion = forceUpdateVersion else { return false }
        let currentVersion = AppConstants.AppInfo.version
        return currentVersion.compare(forceVersion, options: .numeric) == .orderedAscending
    }
}

// MARK: - Feature Flags Model
struct FeatureFlags: Codable {
    let biometricLogin: Bool
    let socialLogin: Bool
    let darkMode: Bool
    let analytics: Bool
    let crashReporting: Bool
    let debugMenu: Bool
    let newRewardSystem: Bool
    let enhancedSecurity: Bool
    let betaFeatures: Bool
    let customConfigs: [String: FeatureFlagConfig]
    
    enum CodingKeys: String, CodingKey {
        case biometricLogin = "biometric_login"
        case socialLogin = "social_login"
        case darkMode = "dark_mode"
        case analytics
        case crashReporting = "crash_reporting"
        case debugMenu = "debug_menu"
        case newRewardSystem = "new_reward_system"
        case enhancedSecurity = "enhanced_security"
        case betaFeatures = "beta_features"
        case customConfigs = "custom_configs"
    }
    
    func isEnabled(_ feature: String) -> Bool {
        switch feature {
        case "biometric_login":
            return biometricLogin
        case "social_login":
            return socialLogin
        case "dark_mode":
            return false // Always disabled - Light mode only
        case "analytics":
            return analytics
        case "crash_reporting":
            return crashReporting
        case "debug_menu":
            return debugMenu
        case "new_reward_system":
            return newRewardSystem
        case "enhanced_security":
            return enhancedSecurity
        case "beta_features":
            return betaFeatures
        default:
            return customConfigs[feature]?.enabled ?? false
        }
    }
    
    func getConfig<T>(_ feature: String, type: T.Type) -> T? {
        guard let config = customConfigs[feature] else { return nil }

        if T.self == String.self {
            return config.stringValue as? T
        } else if T.self == Double.self || T.self == Int.self {
            return config.numberValue as? T
        } else if T.self == Bool.self {
            return config.boolValue as? T
        }

        return nil
    }
}

// MARK: - Feature Flag Config
struct FeatureFlagConfig: Codable {
    let enabled: Bool
    let stringValue: String?
    let numberValue: Double?
    let boolValue: Bool?
    let rolloutPercentage: Double?
    let targetUsers: [String]?

    enum CodingKeys: String, CodingKey {
        case enabled
        case stringValue = "string_value"
        case numberValue = "number_value"
        case boolValue = "bool_value"
        case rolloutPercentage = "rollout_percentage"
        case targetUsers = "target_users"
    }
}

// MARK: - Maintenance Info
struct MaintenanceInfo: Codable {
    let isActive: Bool
    let title: String
    let message: String
    let startTime: Date?
    let endTime: Date?
    let allowedVersions: [String]?
    
    enum CodingKeys: String, CodingKey {
        case isActive = "is_active"
        case title
        case message
        case startTime = "start_time"
        case endTime = "end_time"
        case allowedVersions = "allowed_versions"
    }
    
    var isCurrentlyActive: Bool {
        guard isActive else { return false }
        
        let now = Date()
        
        if let start = startTime, now < start {
            return false
        }
        
        if let end = endTime, now > end {
            return false
        }
        
        return true
    }
    
    var isVersionAllowed: Bool {
        guard let allowedVersions = allowedVersions else { return true }
        return allowedVersions.contains(AppConstants.AppInfo.version)
    }
}

// MARK: - Default Configurations
extension AppConfiguration {
    static var defaultConfig: AppConfiguration {
        return AppConfiguration(
            apiBaseURL: AppConstants.Environment.current.apiBaseURL,
            socketURL: "wss://api.vantis.edu.vn/ws",
            cdnBaseURL: "https://cdn.vantis.edu.vn",
            supportEmail: AppConstants.Contact.supportEmail,
            supportPhone: AppConstants.Contact.supportPhone,
            minAppVersion: "1.0.0",
            forceUpdateVersion: nil,
            exchangeRate: AppConstants.Transaction.exchangeRate,
            transactionFee: AppConstants.Transaction.networkFee,
            maxTransferAmount: AppConstants.Transaction.maxTransferAmount,
            minTransferAmount: AppConstants.Transaction.minTransferAmount,
            sessionTimeout: 3600, // 1 hour
            cacheTimeout: AppConstants.Cache.maxAge,
            retryAttempts: AppConstants.API.retryAttempts,
            retryDelay: AppConstants.API.retryDelay
        )
    }
}

extension FeatureFlags {
    static var defaultFlags: FeatureFlags {
        return FeatureFlags(
            biometricLogin: AppConstants.FeatureFlags.biometricLoginEnabled,
            socialLogin: AppConstants.FeatureFlags.socialLoginEnabled,
            darkMode: false, // Always disabled
            analytics: AppConstants.FeatureFlags.analyticsEnabled,
            crashReporting: AppConstants.FeatureFlags.crashReportingEnabled,
            debugMenu: AppConstants.FeatureFlags.debugMenuEnabled,
            newRewardSystem: true,
            enhancedSecurity: true,
            betaFeatures: false,
            customConfigs: [:]
        )
    }
}
