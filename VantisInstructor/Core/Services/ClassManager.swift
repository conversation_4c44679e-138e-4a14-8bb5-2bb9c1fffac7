//
//  ClassManager.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation
import SwiftUI

@MainActor
class ClassManager: ObservableObject {
    // MARK: - Published Properties
    @Published var classes: [Class] = []
    @Published var todayClasses: [Class] = []
    @Published var upcomingClasses: [Class] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    // API Lessons data
    @Published var apiLessons: [LessonSummary] = []
    @Published var pagination: APIPaginationMetadata?

    // Selected class for detail view
    @Published var selectedClass: Class?

    // MARK: - Services
    private let lessonService: LessonServiceProtocol

    // MARK: - Initialization
    init(lessonService: LessonServiceProtocol = LessonServiceFactory.create(useMockData: true)) {
        self.lessonService = lessonService
        loadClasses()
    }
    
    // MARK: - Data Loading
    func loadClasses() {
        Task {
            await fetchClasses()
        }
    }

    func fetchClasses() async {
        isLoading = true
        errorMessage = nil

        print("🚀 ClassManager.fetchClasses() - Starting API call...")
        print("📱 Using lessonService: \(type(of: lessonService))")

        // TEMPORARY: Set a mock token for testing
        await MainActor.run {
            let tokenManager = TokenManager.shared
            if !tokenManager.isTokenValid {
                print("🧪 Setting mock token for testing...")
                tokenManager.saveToken("mock_test_token_\(UUID().uuidString.prefix(8))", expiresIn: 3600)
                tokenManager.saveUserEmail("<EMAIL>")
            }
        }

        do {
            // Fetch lessons from API
            print("🌐 Calling lessonService.fetchLessons(page: 1, pageSize: 50)...")
            let response = try await lessonService.fetchLessons(page: 1, pageSize: 50, state: nil, classId: nil, dateFrom: nil, dateTo: nil)

            // Store API lessons
            apiLessons = response.data
            pagination = response.pagination

            // Convert API lessons to Class objects for compatibility
            classes = response.data.map { $0.toClass() }

            // Filter classes
            filterClasses()

            print("✅ Fetched \(classes.count) lessons from API")

        } catch {
            print("❌ Failed to fetch lessons from API: \(error)")

            // Handle different error types
            if case NetworkError.unauthorized = error {
                errorMessage = "Cần đăng nhập để xem danh sách lớp học"
                print("⚠️ API requires authentication - using mock data")
            } else {
                errorMessage = "Không thể tải danh sách lớp học: \(error.localizedDescription)"
                print("⚠️ API error - using mock data as fallback")
            }

            // Fallback to mock data
            apiLessons = APILesson.mockAPILessons
            classes = apiLessons.map { $0.toClass() }
            filterClasses()

            print("📝 Using \(classes.count) mock lessons as fallback")
        }

        isLoading = false
    }
    
    private func filterClasses() {
        let calendar = Calendar.current
        let today = Date()
        
        // Today's classes
        todayClasses = classes.filter { classItem in
            calendar.isDate(classItem.scheduledDate, inSameDayAs: today)
        }.sorted { $0.startTime < $1.startTime }
        
        // Upcoming classes (next 7 days)
        let nextWeek = calendar.date(byAdding: .day, value: 7, to: today) ?? today
        upcomingClasses = classes.filter { classItem in
            classItem.scheduledDate > today && classItem.scheduledDate <= nextWeek
        }.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    // MARK: - Class Operations
    func createClass(_ classData: CreateClassData) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            let newClass = Class(
                id: UUID().uuidString,
                courseId: classData.courseId,
                courseName: classData.courseName,
                courseCode: classData.courseCode,
                classNumber: classes.count + 1,
                title: classData.title,
                description: classData.description,
                type: classData.type,
                status: .scheduled,
                scheduledDate: classData.scheduledDate,
                startTime: classData.startTime,
                endTime: classData.endTime,
                duration: Int(classData.endTime.timeIntervalSince(classData.startTime) / 60),
                location: classData.location,
                instructorId: "instructor_1",
                instructorName: "Nguyễn Văn A",
                totalStudents: 35,
                attendedStudents: 0,
                absentStudents: 0,
                lateStudents: 0,
                materials: nil,
                assignments: nil,
                announcements: nil,
                recordingUrl: nil,
                notes: nil,
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            )
            
            classes.append(newClass)
            filterClasses()
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể tạo lớp học: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func updateClass(_ classItem: Class) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            if let index = classes.firstIndex(where: { $0.id == classItem.id }) {
                classes[index] = classItem
                filterClasses()
            }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể cập nhật lớp học: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func deleteClass(_ classId: String) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            classes.removeAll { $0.id == classId }
            filterClasses()
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể xóa lớp học: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func startClass(_ classId: String) async -> Bool {
        guard let index = classes.firstIndex(where: { $0.id == classId }) else {
            return false
        }
        
        var updatedClass = classes[index]
        updatedClass = Class(
            id: updatedClass.id,
            courseId: updatedClass.courseId,
            courseName: updatedClass.courseName,
            courseCode: updatedClass.courseCode,
            classNumber: updatedClass.classNumber,
            title: updatedClass.title,
            description: updatedClass.description,
            type: updatedClass.type,
            status: .inProgress,
            scheduledDate: updatedClass.scheduledDate,
            startTime: updatedClass.startTime,
            endTime: updatedClass.endTime,
            duration: updatedClass.duration,
            location: updatedClass.location,
            instructorId: updatedClass.instructorId,
            instructorName: updatedClass.instructorName,
            totalStudents: updatedClass.totalStudents,
            attendedStudents: updatedClass.attendedStudents,
            absentStudents: updatedClass.absentStudents,
            lateStudents: updatedClass.lateStudents,
            materials: updatedClass.materials,
            assignments: updatedClass.assignments,
            announcements: updatedClass.announcements,
            recordingUrl: updatedClass.recordingUrl,
            notes: updatedClass.notes,
            metadata: updatedClass.metadata,
            createdAt: updatedClass.createdAt,
            updatedAt: Date()
        )
        
        return await updateClass(updatedClass)
    }
    
    func endClass(_ classId: String) async -> Bool {
        guard let index = classes.firstIndex(where: { $0.id == classId }) else {
            return false
        }
        
        var updatedClass = classes[index]
        updatedClass = Class(
            id: updatedClass.id,
            courseId: updatedClass.courseId,
            courseName: updatedClass.courseName,
            courseCode: updatedClass.courseCode,
            classNumber: updatedClass.classNumber,
            title: updatedClass.title,
            description: updatedClass.description,
            type: updatedClass.type,
            status: .completed,
            scheduledDate: updatedClass.scheduledDate,
            startTime: updatedClass.startTime,
            endTime: updatedClass.endTime,
            duration: updatedClass.duration,
            location: updatedClass.location,
            instructorId: updatedClass.instructorId,
            instructorName: updatedClass.instructorName,
            totalStudents: updatedClass.totalStudents,
            attendedStudents: updatedClass.attendedStudents,
            absentStudents: updatedClass.absentStudents,
            lateStudents: updatedClass.lateStudents,
            materials: updatedClass.materials,
            assignments: updatedClass.assignments,
            announcements: updatedClass.announcements,
            recordingUrl: updatedClass.recordingUrl,
            notes: updatedClass.notes,
            metadata: updatedClass.metadata,
            createdAt: updatedClass.createdAt,
            updatedAt: Date()
        )
        
        return await updateClass(updatedClass)
    }
    
    // MARK: - Utility Methods
    func getClassesForDate(_ date: Date) -> [Class] {
        let calendar = Calendar.current
        return classes.filter { classItem in
            calendar.isDate(classItem.scheduledDate, inSameDayAs: date)
        }.sorted { $0.startTime < $1.startTime }
    }

    func getAPILessonsForDate(_ date: Date) -> [APILesson] {
        let calendar = Calendar.current
        return apiLessons.filter { lesson in
            calendar.isDate(lesson.startDate, inSameDayAs: date)
        }.sorted { $0.startDate < $1.startDate }
    }
    
    func getClassesForCourse(_ courseId: String) -> [Class] {
        return classes.filter { $0.courseId == courseId }
            .sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    func refreshData() async {
        await fetchClasses()
    }

    // MARK: - API Lesson Helpers
    func getAPILessonById(_ id: Int) -> APILesson? {
        return apiLessons.first { $0.id == id }
    }

    func getTodayAPILessons() -> [APILesson] {
        return apiLessons.filter { $0.isToday }
    }

    func getUpcomingAPILessons() -> [APILesson] {
        return apiLessons.filter { $0.isUpcoming }
    }

    func getOverdueAPILessons() -> [APILesson] {
        return apiLessons.filter { $0.isOverdue }
    }

    func getLessonsNeedingAttention() -> [APILesson] {
        return apiLessons.filter { $0.needsAttention }
    }

    func getTodayClassesCount() -> Int {
        return todayClasses.count
    }

    func getUpcomingClassesCount() -> Int {
        return upcomingClasses.count
    }

    func getAverageAttendanceRate() -> Double {
        let totalClasses = classes.filter { $0.status == .completed }
        guard !totalClasses.isEmpty else { return 0.0 }

        let totalRate = totalClasses.reduce(0.0) { sum, classItem in
            let rate = Double(classItem.attendedStudents) / Double(classItem.totalStudents) * 100
            return sum + rate
        }

        return totalRate / Double(totalClasses.count)
    }
}

// MARK: - Create Class Data
struct CreateClassData {
    let courseId: String
    let courseName: String
    let courseCode: String
    let title: String
    let description: String?
    let type: ClassType
    let scheduledDate: Date
    let startTime: Date
    let endTime: Date
    let location: ClassLocation?
}

// MARK: - Extensions
extension ClassManager {
    var hasClassesToday: Bool {
        !todayClasses.isEmpty
    }
    
    var nextClass: Class? {
        todayClasses.first { $0.isUpcoming }
    }
    
    var currentClass: Class? {
        todayClasses.first { $0.status == .inProgress }
    }
    
    var completedClassesToday: [Class] {
        todayClasses.filter { $0.status == .completed }
    }
    
    var todayAttendanceRate: Double {
        let completedClasses = completedClassesToday
        guard !completedClasses.isEmpty else { return 0 }
        
        let totalRate = completedClasses.reduce(0.0) { sum, classItem in
            sum + classItem.attendanceRate
        }
        
        return totalRate / Double(completedClasses.count)
    }
}
