//
//  Extensions.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI

// MARK: - String Extensions
extension String {
    var isValidEmail: Bool {
        let emailRegex = AppConstants.Validation.emailRegex
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: self)
    }
    
    var isValidPhone: Bool {
        let phoneRegex = AppConstants.Validation.phoneRegex
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: self)
    }
    
    var isValidPassword: Bool {
        return count >= AppConstants.Validation.minPasswordLength && 
               count <= AppConstants.Validation.maxPasswordLength
    }
    
    func capitalizingFirstLetter() -> String {
        return prefix(1).capitalized + dropFirst()
    }
    
    func truncated(to length: Int, trailing: String = "...") -> String {
        return count > length ? prefix(length) + trailing : self
    }
    
    func formatAsPhoneNumber() -> String {
        let cleanNumber = components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        
        if cleanNumber.count == 10 {
            let areaCode = String(cleanNumber.prefix(3))
            let firstThree = String(cleanNumber.dropFirst(3).prefix(3))
            let lastFour = String(cleanNumber.suffix(4))
            return "(\(areaCode)) \(firstThree)-\(lastFour)"
        }
        
        return self
    }
}

// MARK: - Double Extensions
extension Double {
    func formatAsCurrency(symbol: String = "VND") -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 0
        
        if let formattedNumber = formatter.string(from: NSNumber(value: self)) {
            return "\(formattedNumber) \(symbol)"
        }
        
        return "\(Int(self)) \(symbol)"
    }
    
    func formatAsToken(symbol: String = "LXT") -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 2
        
        if let formattedNumber = formatter.string(from: NSNumber(value: self)) {
            return "\(formattedNumber) \(symbol)"
        }
        
        return String(format: "%.2f %@", self, symbol)
    }
    
    func formatAsPercentage() -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .percent
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 1
        
        return formatter.string(from: NSNumber(value: self)) ?? "\(Int(self * 100))%"
    }
}

// MARK: - Date Extensions
extension Date {
    func timeAgoDisplay() -> String {
        let calendar = Calendar.current
        let now = Date()
        
        if calendar.isDateInToday(self) {
            let components = calendar.dateComponents([.hour, .minute], from: self, to: now)
            
            if let hours = components.hour, hours > 0 {
                return "\(hours)h ago"
            } else if let minutes = components.minute, minutes > 0 {
                return "\(minutes)m ago"
            } else {
                return "Just now"
            }
        } else if calendar.isDateInYesterday(self) {
            return "Yesterday"
        } else {
            let components = calendar.dateComponents([.day], from: self, to: now)
            if let days = components.day, days < 7 {
                return "\(days)d ago"
            } else {
                let formatter = DateFormatter()
                formatter.dateStyle = .short
                return formatter.string(from: self)
            }
        }
    }
    
    func formatted(style: DateFormatter.Style = .medium) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = style
        return formatter.string(from: self)
    }
    
    func formatted(format: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.string(from: self)
    }
}

// MARK: - View Extensions
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
    
    func cardStyle() -> some View {
        self
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.1), radius: AppConstants.UI.shadowRadius, x: 0, y: 2)
    }
    
    func primaryButtonStyle() -> some View {
        self
            .frame(height: AppConstants.UI.buttonHeight)
            .background(AppConstants.Colors.primary)
            .foregroundColor(.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    func secondaryButtonStyle() -> some View {
        self
            .frame(height: AppConstants.UI.buttonHeight)
            .background(AppConstants.Colors.surface)
            .foregroundColor(AppConstants.Colors.primary)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.primary, lineWidth: 1)
            )
    }

    // MARK: - Modern Button Styles
    func modernPrimaryButtonStyle(isLoading: Bool = false, isDisabled: Bool = false) -> some View {
        self
            .font(.beVietnamPro(.semiBold, size: 16))
            .foregroundColor(.white)
            .frame(height: 52)
            .frame(maxWidth: .infinity)
            .background(
                Group {
                    if isDisabled {
                        AppConstants.Colors.primary.opacity(0.4)
                    } else {
                        LinearGradient(
                            gradient: Gradient(colors: [
                                AppConstants.Colors.primary,
                                AppConstants.Colors.primaryDark
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    }
                }
            )
            .cornerRadius(16)
            .shadow(
                color: isDisabled ? Color.clear : AppConstants.Colors.primary.opacity(0.3),
                radius: 12,
                x: 0,
                y: 6
            )
            .scaleEffect(isDisabled ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isDisabled)
            .overlay(
                Group {
                    if isLoading {
                        HStack(spacing: 8) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                            Text("Đang xử lý...")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(.white)
                        }
                    }
                }
            )
    }

    func modernSecondaryButtonStyle() -> some View {
        self
            .font(.beVietnamPro(.medium, size: 16))
            .foregroundColor(AppConstants.Colors.primary)
            .frame(height: 52)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        AppConstants.Colors.primary.opacity(0.3),
                                        AppConstants.Colors.primaryDark.opacity(0.3)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )
            )
            .shadow(
                color: Color.black.opacity(0.05),
                radius: 8,
                x: 0,
                y: 2
            )
            .scaleEffect(1.0)
            .animation(.easeInOut(duration: 0.15), value: true)
    }
    
    func textFieldStyle() -> some View {
        self
            .padding()
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
    }
    
    func hapticFeedback(_ type: AppConstants.HapticType) -> some View {
        self.onTapGesture {
            HapticManager.shared.trigger(type)
        }
    }
    
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content
    ) -> some View {
        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

// MARK: - Custom Shapes
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - Haptic Manager
class HapticManager {
    static let shared = HapticManager()
    
    private init() {}
    
    func trigger(_ type: AppConstants.HapticType) {
        switch type {
        case .light:
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        case .medium:
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        case .heavy:
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
        case .success:
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
        case .warning:
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.warning)
        case .error:
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
        }
    }
}

// MARK: - Loading State
enum LoadingState {
    case idle
    case loading
    case success
    case failure(Error)
    
    var isLoading: Bool {
        if case .loading = self {
            return true
        }
        return false
    }
    
    var error: Error? {
        if case .failure(let error) = self {
            return error
        }
        return nil
    }
}

// MARK: - Result Extensions
extension Result {
    var isSuccess: Bool {
        switch self {
        case .success:
            return true
        case .failure:
            return false
        }
    }
    
    var isFailure: Bool {
        return !isSuccess
    }
    
    var value: Success? {
        switch self {
        case .success(let value):
            return value
        case .failure:
            return nil
        }
    }
    
    var error: Failure? {
        switch self {
        case .success:
            return nil
        case .failure(let error):
            return error
        }
    }
}
