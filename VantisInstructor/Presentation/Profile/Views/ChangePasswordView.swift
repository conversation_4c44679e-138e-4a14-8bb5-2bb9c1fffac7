//
//  ChangePasswordView.swift
//  VantisInstructor
//
//  Created for change password functionality
//

import SwiftUI

struct ChangePasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var showError = false
    @State private var errorMessage: String?
    @State private var animateElements = false
    @State private var showCurrentPassword = false
    @State private var showNewPassword = false
    @State private var showConfirmPassword = false

    var body: some View {
        ZStack {
            // Background
            AppConstants.Colors.background
                .ignoresSafeArea(.all)

            ScrollView {
                VStack(spacing: 32) {
                    // Custom Header
                    headerSection
                    
                    // Password Form
                    passwordFormSection
                    
                    // Action Buttons
                    actionButtonsSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                animateElements = true
            }
        }
        .overlay(
            Group {
                if showSuccess {
                    customSuccessOverlay
                }
            }
        )
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = nil
            }
        } message: {
            Text(errorMessage ?? "An error occurred")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                HapticManager.shared.trigger(.light)
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Cancel")
                        .font(.beVietnamPro(.medium, size: 16))
                }
                .foregroundColor(AppConstants.Colors.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.9))
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
            .opacity(animateElements ? 1 : 0)
            .offset(x: animateElements ? 0 : -20)
            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: animateElements)

            Spacer()
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - Password Form Section
    private var passwordFormSection: some View {
        VStack(spacing: 24) {
            // Title and Description
            VStack(spacing: 12) {
                Text("Đổi mật khẩu")
                    .font(.beVietnamPro(.bold, size: 28))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .opacity(animateElements ? 1 : 0)
                    .offset(y: animateElements ? 0 : 20)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateElements)
                
                Text("Nhập mật khẩu hiện tại và mật khẩu mới để cập nhật bảo mật tài khoản của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
                    .opacity(animateElements ? 1 : 0)
                    .offset(y: animateElements ? 0 : 20)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: animateElements)
            }
            
            // Password Fields
            VStack(spacing: 20) {
                // Current Password Field
                currentPasswordField

                // New Password Field
                newPasswordField

                // Confirm Password Field
                confirmPasswordField
            }
        }
    }

    // MARK: - Current Password Field
    private var currentPasswordField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Mật khẩu hiện tại")
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)

            HStack {
                Group {
                    if showCurrentPassword {
                        TextField("Nhập mật khẩu hiện tại", text: $currentPassword)
                    } else {
                        SecureField("Nhập mật khẩu hiện tại", text: $currentPassword)
                    }
                }
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)

                Button(action: {
                    HapticManager.shared.trigger(.light)
                    showCurrentPassword.toggle()
                }) {
                    Image(systemName: showCurrentPassword ? "eye.slash" : "eye")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        currentPassword.isEmpty ? AppConstants.Colors.border : AppConstants.Colors.primary.opacity(0.3),
                        lineWidth: currentPassword.isEmpty ? 1 : 1.5
                    )
            )
        }
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateElements)
    }

    // MARK: - New Password Field
    private var newPasswordField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Mật khẩu mới")
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)

            HStack {
                Group {
                    if showNewPassword {
                        TextField("Nhập mật khẩu mới", text: $newPassword)
                    } else {
                        SecureField("Nhập mật khẩu mới", text: $newPassword)
                    }
                }
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)

                Button(action: {
                    HapticManager.shared.trigger(.light)
                    showNewPassword.toggle()
                }) {
                    Image(systemName: showNewPassword ? "eye.slash" : "eye")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        newPassword.isEmpty ? AppConstants.Colors.border :
                        (isPasswordValid(newPassword) ? AppConstants.Colors.success.opacity(0.5) : AppConstants.Colors.error.opacity(0.5)),
                        lineWidth: newPassword.isEmpty ? 1 : 1.5
                    )
            )

            // Password Requirements
            if !newPassword.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    passwordRequirement("Ít nhất 8 ký tự", isValid: newPassword.count >= 8)
                    passwordRequirement("Có chữ hoa và chữ thường", isValid: hasUpperAndLowerCase(newPassword))
                    passwordRequirement("Có ít nhất 1 số", isValid: hasNumber(newPassword))
                }
                .padding(.top, 4)
            }
        }
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: animateElements)
    }

    // MARK: - Confirm Password Field
    private var confirmPasswordField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Xác nhận mật khẩu mới")
                .font(.beVietnamPro(.semiBold, size: 14))
                .foregroundColor(AppConstants.Colors.textPrimary)

            HStack {
                Group {
                    if showConfirmPassword {
                        TextField("Nhập lại mật khẩu mới", text: $confirmPassword)
                    } else {
                        SecureField("Nhập lại mật khẩu mới", text: $confirmPassword)
                    }
                }
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)

                Button(action: {
                    HapticManager.shared.trigger(.light)
                    showConfirmPassword.toggle()
                }) {
                    Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        confirmPassword.isEmpty ? AppConstants.Colors.border :
                        (confirmPassword == newPassword ? AppConstants.Colors.success.opacity(0.5) : AppConstants.Colors.error.opacity(0.5)),
                        lineWidth: confirmPassword.isEmpty ? 1 : 1.5
                    )
            )

            // Password Match Indicator
            if !confirmPassword.isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: confirmPassword == newPassword ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(confirmPassword == newPassword ? AppConstants.Colors.success : AppConstants.Colors.error)

                    Text(confirmPassword == newPassword ? "Mật khẩu khớp" : "Mật khẩu không khớp")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(confirmPassword == newPassword ? AppConstants.Colors.success : AppConstants.Colors.error)
                }
                .padding(.top, 4)
            }
        }
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateElements)
    }

    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // Change Password Button
            Button(action: {
                HapticManager.shared.trigger(.medium)
                Task {
                    await changePassword()
                }
            }) {
                HStack(spacing: 12) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)
                    } else {
                        Image(systemName: "key.fill")
                            .font(.system(size: 16, weight: .semibold))
                    }

                    Text(isLoading ? "Đang cập nhật..." : "Cập nhật mật khẩu")
                        .font(.beVietnamPro(.semiBold, size: 16))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppConstants.Colors.primary,
                                    AppConstants.Colors.primary.opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: AppConstants.Colors.primary.opacity(0.3),
                            radius: 12,
                            x: 0,
                            y: 6
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(!isFormValid || isLoading)
            .scaleEffect(isFormValid && !isLoading ? 1.0 : 0.98)
            .opacity(animateElements ? 1 : 0)
            .offset(y: animateElements ? 0 : 20)
            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: animateElements)
            .animation(.easeInOut(duration: 0.2), value: isFormValid)
            .animation(.easeInOut(duration: 0.2), value: isLoading)
        }
    }

    // MARK: - Helper Methods
    private var isFormValid: Bool {
        !currentPassword.isEmpty &&
        isPasswordValid(newPassword) &&
        confirmPassword == newPassword
    }

    private func isPasswordValid(_ password: String) -> Bool {
        password.count >= 8 &&
        hasUpperAndLowerCase(password) &&
        hasNumber(password)
    }

    private func hasUpperAndLowerCase(_ password: String) -> Bool {
        password.rangeOfCharacter(from: .uppercaseLetters) != nil &&
        password.rangeOfCharacter(from: .lowercaseLetters) != nil
    }

    private func hasNumber(_ password: String) -> Bool {
        password.rangeOfCharacter(from: .decimalDigits) != nil
    }

    private func passwordRequirement(_ text: String, isValid: Bool) -> some View {
        HStack(spacing: 8) {
            Image(systemName: isValid ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(isValid ? AppConstants.Colors.success : AppConstants.Colors.textSecondary)

            Text(text)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(isValid ? AppConstants.Colors.success : AppConstants.Colors.textSecondary)
        }
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }

    // MARK: - Custom Success Overlay
    private var customSuccessOverlay: some View {
        ZStack {
            // Background blur
            Color.black.opacity(0.4)
                .ignoresSafeArea(.all)

            // Success card
            VStack(spacing: 24) {
                // Success icon with animation
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppConstants.Colors.success.opacity(0.1),
                                    AppConstants.Colors.success.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(AppConstants.Colors.success)
                        .scaleEffect(showSuccess ? 1.0 : 0.5)
                        .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.1), value: showSuccess)
                }

                // Success content
                VStack(spacing: 16) {
                    Text("Thành công!")
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: showSuccess)

                    Text("Mật khẩu của bạn đã được cập nhật thành công.")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(2)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: showSuccess)
                }

                // Action button
                Button(action: {
                    HapticManager.shared.trigger(.light)
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        showSuccess = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        dismiss()
                    }
                }) {
                    Text("Hoàn tất")
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppConstants.Colors.success,
                                            AppConstants.Colors.success.opacity(0.8)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(
                                    color: AppConstants.Colors.success.opacity(0.3),
                                    radius: 8,
                                    x: 0,
                                    y: 4
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .opacity(showSuccess ? 1 : 0)
                .offset(y: showSuccess ? 0 : 20)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: showSuccess)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showSuccess ? 1.0 : 0.8)
            .opacity(showSuccess ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showSuccess)
        }
    }

    // MARK: - Change Password Action
    private func changePassword() async {
        guard isFormValid else {
            showErrorMessage("Vui lòng kiểm tra lại thông tin")
            return
        }

        isLoading = true
        hideKeyboard()

        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

            // TODO: Implement actual change password API call
            // let response = try await APIClient.shared.request(
            //     endpoint: "/auth/change-password",
            //     method: .POST,
            //     parameters: [
            //         "current_password": currentPassword,
            //         "new_password": newPassword
            //     ],
            //     responseType: EmptyResponse.self
            // )

            HapticManager.shared.trigger(.success)
            showSuccess = true

        } catch {
            HapticManager.shared.trigger(.error)
            showErrorMessage(error.localizedDescription)
        }

        isLoading = false
    }
}

#Preview {
    ChangePasswordView()
}
