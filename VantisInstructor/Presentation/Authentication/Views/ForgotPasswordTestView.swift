//
//  ForgotPasswordTestView.swift
//  VantisInstructor
//
//  Created for testing custom success popup
//

import SwiftUI

struct ForgotPasswordTestView: View {
    @State private var showSuccess = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 40) {
            Text("Test Custom Success Popup")
                .font(.beVietnamPro(.bold, size: 24))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            <PERSON><PERSON>("Show Success Popup") {
                HapticManager.shared.trigger(.success)
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showSuccess = true
                }
            }
            .modernPrimaryButtonStyle()
            
            <PERSON><PERSON>("Close") {
                dismiss()
            }
            .modernSecondaryButtonStyle()
        }
        .padding()
        .overlay(
            Group {
                if showSuccess {
                    customSuccessOverlay
                }
            }
        )
    }
    
    // MARK: - Custom Success Overlay
    private var customSuccessOverlay: some View {
        ZStack {
            // Background blur
            Color.black.opacity(0.4)
                .ignoresSafeArea(.all)
                .onTapGesture {
                    // Prevent dismissing by tapping background
                }
            
            // Success card
            VStack(spacing: 24) {
                // Success icon with animation
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppConstants.Colors.success.opacity(0.1),
                                    AppConstants.Colors.success.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(AppConstants.Colors.success)
                        .scaleEffect(showSuccess ? 1.0 : 0.5)
                        .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.1), value: showSuccess)
                }
                
                // Success content
                VStack(spacing: 16) {
                    Text("Success!")
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: showSuccess)
                    
                    Text("Password reset instructions have been sent to your email address.")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(2)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: showSuccess)
                }
                
                // Action button
                Button(action: {
                    HapticManager.shared.trigger(.light)
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        showSuccess = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        dismiss()
                    }
                }) {
                    Text("Got it!")
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppConstants.Colors.success,
                                            AppConstants.Colors.success.opacity(0.8)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(
                                    color: AppConstants.Colors.success.opacity(0.3),
                                    radius: 8,
                                    x: 0,
                                    y: 4
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .opacity(showSuccess ? 1 : 0)
                .offset(y: showSuccess ? 0 : 20)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: showSuccess)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showSuccess ? 1.0 : 0.8)
            .opacity(showSuccess ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showSuccess)
        }
    }
}

#Preview {
    ForgotPasswordTestView()
}
