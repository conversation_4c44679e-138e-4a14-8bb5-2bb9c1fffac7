//
//  ForgotPasswordView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var email = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var errorMessage: String?
    @State private var showError = false
    @FocusState private var isEmailFocused: Bool
    @State private var animateGradient = false
    @State private var animateElements = false

    var body: some View {
        ZStack {
            // Modern gradient background
            modernGradientBackground
                .ignoresSafeArea(.all)
                .allowsHitTesting(false)

            VStack(spacing: 0) {
                // Custom navigation bar
                customNavigationBar
                    .padding(.top, 10)

                Spacer()

                // Main content card
                mainContentCard
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            animateElements = true
            animateGradient = true
            // Auto-focus on email field
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isEmailFocused = true
            }
        }
        .onTapGesture {
            hideKeyboard()
        }
        // Custom Success Overlay
        .overlay(
            Group {
                if showSuccess {
                    customSuccessOverlay
                }
            }
        )
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = nil
            }
        } message: {
            Text(errorMessage ?? "An error occurred")
        }
    }

    // MARK: - Modern Gradient Background
    private var modernGradientBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.95, green: 0.97, blue: 1.0),
                    Color(red: 0.92, green: 0.95, blue: 0.98),
                    Color(red: 0.88, green: 0.92, blue: 0.96)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Animated floating elements
            ForEach(0..<6, id: \.self) { index in
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                AppConstants.Colors.primary.opacity(0.1),
                                AppConstants.Colors.primary.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: CGFloat.random(in: 60...120))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .scaleEffect(animateGradient ? 1.2 : 0.8)
                    .opacity(animateGradient ? 0.6 : 0.3)
                    .animation(
                        Animation.easeInOut(duration: Double.random(in: 3...6))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.5),
                        value: animateGradient
                    )
            }
        }
    }

    // MARK: - Custom Navigation Bar
    private var customNavigationBar: some View {
        HStack {
            Button(action: {
                HapticManager.shared.trigger(.light)
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Cancel")
                        .font(.beVietnamPro(.medium, size: 16))
                }
                .foregroundColor(AppConstants.Colors.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.9))
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()
        }
        .padding(.horizontal, 20)
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : -20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: animateElements)
    }

    // MARK: - Main Content Card
    private var mainContentCard: some View {
        VStack(spacing: 0) {
            // Header section
            headerSection
                .padding(.top, 40)
                .padding(.bottom, 32)

            // Form section
            formSection
                .padding(.bottom, 32)

            // Action buttons
            actionButtonsSection
                .padding(.bottom, 40)
        }
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.08), radius: 20, x: 0, y: 8)
                .shadow(color: Color.black.opacity(0.04), radius: 1, x: 0, y: 1)
        )
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 30)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: animateElements)
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 20) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                AppConstants.Colors.primary.opacity(0.1),
                                AppConstants.Colors.primary.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)

                Image(systemName: "lock.rotation")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(AppConstants.Colors.primary)
            }
            .scaleEffect(animateElements ? 1 : 0.8)
            .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.5), value: animateElements)

            VStack(spacing: 12) {
                Text("Forgot Password?")
                    .font(.beVietnamPro(.bold, size: 28))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .opacity(animateElements ? 1 : 0)
                    .offset(y: animateElements ? 0 : 10)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateElements)

                Text("Enter your email address and we'll send you\ninstructions to reset your password.")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
                    .opacity(animateElements ? 1 : 0)
                    .offset(y: animateElements ? 0 : 10)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: animateElements)
            }
        }
        .padding(.horizontal, 24)
    }

    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Email Address")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                VStack(spacing: 8) {
                    HStack(spacing: 12) {
                        Image(systemName: "envelope")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(isEmailFocused ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                            .frame(width: 20)

                        TextField("Enter your email", text: $email)
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .keyboardType(.emailAddress)
                            .textContentType(.emailAddress)
                            .autocapitalization(.none)
                            .focused($isEmailFocused)
                            .onSubmit {
                                Task {
                                    await resetPassword()
                                }
                            }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        isEmailFocused ? AppConstants.Colors.primary :
                                        (!email.isEmpty && !email.isValidEmail ? AppConstants.Colors.error : Color.gray.opacity(0.2)),
                                        lineWidth: isEmailFocused ? 2 : 1
                                    )
                            )
                    )
                    .animation(.easeInOut(duration: 0.2), value: isEmailFocused)

                    if !email.isEmpty && !email.isValidEmail {
                        HStack {
                            Image(systemName: "exclamationmark.circle.fill")
                                .font(.system(size: 12))
                                .foregroundColor(AppConstants.Colors.error)

                            Text("Please enter a valid email address")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.error)

                            Spacer()
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
            }
        }
        .padding(.horizontal, 24)
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.8), value: animateElements)
    }

    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 20) {
            // Send Reset Instructions Button
            Button(action: {
                HapticManager.shared.trigger(.medium)
                Task {
                    await resetPassword()
                }
            }) {
                HStack(spacing: 12) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)
                    } else {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 16, weight: .semibold))
                    }

                    Text(isLoading ? "Sending..." : "Send Reset Instructions")
                        .font(.beVietnamPro(.semiBold, size: 16))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 52)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    email.isValidEmail && !isLoading ? AppConstants.Colors.primary : Color.gray.opacity(0.6),
                                    email.isValidEmail && !isLoading ? AppConstants.Colors.primary.opacity(0.8) : Color.gray.opacity(0.4)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: email.isValidEmail && !isLoading ? AppConstants.Colors.primary.opacity(0.3) : Color.clear,
                            radius: 8,
                            x: 0,
                            y: 4
                        )
                )
                .foregroundColor(.white)
            }
            .disabled(!email.isValidEmail || isLoading)
            .scaleEffect(email.isValidEmail && !isLoading ? 1.0 : 0.98)
            .animation(.easeInOut(duration: 0.2), value: email.isValidEmail)
            .animation(.easeInOut(duration: 0.2), value: isLoading)

            // Back to Sign In Button
            Button(action: {
                HapticManager.shared.trigger(.light)
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.left")
                        .font(.system(size: 14, weight: .medium))

                    Text("Back to Sign In")
                        .font(.beVietnamPro(.semiBold, size: 16))
                }
                .foregroundColor(AppConstants.Colors.primary)
                .padding(.vertical, 12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 24)
        .opacity(animateElements ? 1 : 0)
        .offset(y: animateElements ? 0 : 20)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.9), value: animateElements)
    }

    // MARK: - Custom Success Overlay
    private var customSuccessOverlay: some View {
        ZStack {
            // Background blur
            Color.black.opacity(0.4)
                .ignoresSafeArea(.all)
                .onTapGesture {
                    // Prevent dismissing by tapping background
                }

            // Success card
            VStack(spacing: 24) {
                // Success icon with animation
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppConstants.Colors.success.opacity(0.1),
                                    AppConstants.Colors.success.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(AppConstants.Colors.success)
                        .scaleEffect(showSuccess ? 1.0 : 0.5)
                        .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.1), value: showSuccess)
                }

                // Success content
                VStack(spacing: 16) {
                    Text("Success!")
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: showSuccess)

                    Text("Password reset instructions have been sent to your email address.")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(2)
                        .opacity(showSuccess ? 1 : 0)
                        .offset(y: showSuccess ? 0 : 10)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: showSuccess)
                }

                // Action button
                Button(action: {
                    HapticManager.shared.trigger(.light)
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        showSuccess = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        dismiss()
                    }
                }) {
                    Text("Got it!")
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppConstants.Colors.success,
                                            AppConstants.Colors.success.opacity(0.8)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(
                                    color: AppConstants.Colors.success.opacity(0.3),
                                    radius: 8,
                                    x: 0,
                                    y: 4
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .opacity(showSuccess ? 1 : 0)
                .offset(y: showSuccess ? 0 : 20)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: showSuccess)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showSuccess ? 1.0 : 0.8)
            .opacity(showSuccess ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showSuccess)
        }
    }

    // MARK: - Reset Password Action
    private func resetPassword() async {
        guard email.isValidEmail else {
            showErrorMessage("Please enter a valid email address")
            return
        }

        isLoading = true
        hideKeyboard()

        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

            // TODO: Implement actual password reset API call
            // let response = try await APIClient.shared.request(
            //     endpoint: "/auth/forgot-password",
            //     method: .POST,
            //     parameters: ["email": email],
            //     responseType: EmptyResponse.self
            // )

            HapticManager.shared.trigger(.success)
            showSuccess = true

        } catch {
            HapticManager.shared.trigger(.error)
            showErrorMessage(error.localizedDescription)
        }

        isLoading = false
    }

    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Preview
#Preview {
    ForgotPasswordView()
}
