//
//  RecentClassesSection.swift
//  VantisInstructor
//
//  Created by Instructor App on 30/7/25.
//

import SwiftUI

struct RecentClassesSection: View {
    @ObservedObject var classManager: ClassManager
    let onViewAll: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Lớp học gần đây")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()

                Button("Xem tất cả") {
                    onViewAll()
                }
                .font(AppConstants.Typography.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }

            if classManager.isLoading {
                VStack {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text("Đang tải...")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.top, 8)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(12)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(Array(classManager.classes.prefix(3))) { classItem in
                        RecentClassCard(classItem: classItem) {
                            onViewAll()
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Recent Class Card (Based on APILessonCard design)
struct RecentClassCard: View {
    let classItem: Class
    let onTap: () -> Void
    @State private var showingLessonDetail = false

    var body: some View {
        Button(action: {
            showingLessonDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with thumbnail and course info
                HStack(alignment: .top, spacing: 12) {
                    // Thumbnail icon
                    LessonThumbnailIcon()

                    // Course info
                    VStack(alignment: .leading, spacing: 8) {
                        Text(classItem.courseCode)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text(classItem.title)
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        // Status badge below title
                        HStack {
                            StatusBadge(
                                text: classItem.status.displayName,
                                color: statusColorString(for: classItem.status),
                                isToday: classItem.isToday,
                                needsAttention: classItem.status == .postponed || classItem.status == .cancelled
                            )

                            Spacer()
                        }
                    }
                }

                // Date, time and location info - aligned with title
                HStack(alignment: .top, spacing: 12) {
                    // Empty space to align with title (same width as thumbnail + spacing)
                    Spacer()
                        .frame(width: 48 + 12)

                    VStack(spacing: 8) {
                        // Date and time info (same line)
                        HStack(spacing: 16) {
                            // Date
                            HStack(spacing: 6) {
                                Image(systemName: "calendar")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)

                                Text(formattedDate(for: classItem.scheduledDate))
                                    .font(AppConstants.Typography.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }

                            // Time
                            HStack(spacing: 6) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)

                                Text(classItem.formattedTime)
                                    .font(AppConstants.Typography.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }

                            Spacer()
                        }

                        // Location info (separate line)
                        HStack(spacing: 6) {
                            Image(systemName: "location")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text("Phòng \(classItem.location?.room ?? "TBD")")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Spacer()
                        }
                    }
                }

                // Attendance info (only for completed lessons) - aligned with title
                if classItem.status == .completed {
                    HStack(alignment: .top, spacing: 12) {
                        // Empty space to align with title
                        Spacer()
                            .frame(width: 48 + 12)

                        attendanceInfoView
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLessonDetail) {
            LessonDetailBottomSheet(lesson: classItem.toAPILesson())
        }
    }

    // MARK: - Attendance Info View
    private var attendanceInfoView: some View {
        HStack(spacing: 12) {
            // Present count
            HStack(spacing: 4) {
                Image(systemName: "person.fill")
                    .font(.caption2)
                    .foregroundColor(.green)

                Text("\(classItem.attendedStudents)/\(classItem.totalStudents)")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            // Attendance rate
            HStack(spacing: 4) {
                Image(systemName: "chart.bar.fill")
                    .font(.caption2)
                    .foregroundColor(attendanceRateColor)

                Text("\(Int(classItem.attendanceRate))%")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(attendanceRateColor)
                    .fontWeight(.medium)
            }

            Spacer()
        }
    }

    // MARK: - Helper Methods
    private func formattedDate(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        return formatter.string(from: date)
    }

    private func statusColorString(for status: ClassStatus) -> String {
        switch status {
        case .scheduled: return "blue"
        case .inProgress: return "green"
        case .completed: return "gray"
        case .cancelled: return "red"
        case .postponed: return "orange"
        }
    }

    private var attendanceRateColor: Color {
        if classItem.attendanceRate >= 90 {
            return .green
        } else if classItem.attendanceRate >= 70 {
            return .blue
        } else if classItem.attendanceRate >= 50 {
            return .orange
        } else {
            return .red
        }
    }
}



// MARK: - Preview
#Preview {
    RecentClassesSection(
        classManager: ClassManager(),
        onViewAll: {}
    )
}
