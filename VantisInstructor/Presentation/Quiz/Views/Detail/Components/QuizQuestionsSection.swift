//
//  QuizQuestionsSection.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct QuizQuestionsSection: View {
    @ObservedObject var viewModel: QuizDetailViewModel
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            if viewModel.isLoadingQuestions {
                loadingView
            } else if viewModel.questions.isEmpty {
                emptyQuestionsView
            } else {
                questionsContent
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải câu hỏi...")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    // MARK: - Empty Questions View
    private var emptyQuestionsView: some View {
        VStack(spacing: 32) {
            // Modern empty state illustration
            ZStack {
                RoundedRectangle(cornerRadius: 24)
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "questionmark.circle.fill")
                    .font(.system(size: 48))
                    .foregroundColor(AppConstants.Colors.primary)
            }

            VStack(spacing: 12) {
                Text("Chưa có câu hỏi")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Thêm câu hỏi để hoàn thiện quiz của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }

            if viewModel.canEdit {
                addQuestionButton
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 50)
        .padding(.horizontal, 24)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }

    // MARK: - Add Question Button
    private var addQuestionButton: some View {
        Button(action: {
            viewModel.showingEditQuiz = true
        }) {
            HStack(spacing: 12) {
                Image(systemName: "plus.circle.fill")
                    .font(.system(size: 20, weight: .medium))

                Text("Thêm câu hỏi")
                    .font(.beVietnamPro(.semiBold, size: 16))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 52)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppConstants.Colors.primary,
                        AppConstants.Colors.primary.opacity(0.8)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(
                color: AppConstants.Colors.primary.opacity(0.3),
                radius: 8,
                x: 0,
                y: 4
            )
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 8) // Add padding to prevent overflow
    }
    
    // MARK: - Questions Content
    private var questionsContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Questions Header
            questionsHeader
            
            // Questions List
            questionsList
        }
    }
    
    // MARK: - Questions Header
    private var questionsHeader: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Danh sách câu hỏi")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                if viewModel.canEdit {
                    Button("Chỉnh sửa") {
                        viewModel.showingEditQuiz = true
                    }
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            
            // Summary Stats
            HStack(spacing: 20) {
                StatItem(
                    label: "Tổng câu hỏi",
                    value: "\(viewModel.quiz.questionCount)"
                )
                
                StatItem(
                    label: "Tổng điểm",
                    value: "\(Int(viewModel.quiz.maxScore))"
                )
                
                if viewModel.hasAnalytics {
                    StatItem(
                        label: "Độ khó TB",
                        value: averageDifficulty
                    )
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    private var averageDifficulty: String {
        let questionAnalytics = viewModel.getQuestionAnalyticsData()
        guard !questionAnalytics.isEmpty else { return "N/A" }

        let totalDifficulty = questionAnalytics.reduce(0) { sum, question in
            sum + difficultyValue(question.difficulty)
        }
        let average = Double(totalDifficulty) / Double(questionAnalytics.count)

        if average <= 1.3 {
            return "Dễ"
        } else if average <= 2.3 {
            return "Trung bình"
        } else {
            return "Khó"
        }
    }

    private func difficultyValue(_ difficulty: QuestionDifficulty) -> Int {
        switch difficulty {
        case .easy: return 1
        case .medium: return 2
        case .hard: return 3
        }
    }
    
    // MARK: - Questions List
    private var questionsList: some View {
        VStack(spacing: 12) {
            // Mock questions for now
            ForEach(1...viewModel.quiz.questionCount, id: \.self) { index in
                QuestionDetailRow(
                    questionNumber: index,
                    questionText: mockQuestionText(for: index),
                    questionType: mockQuestionType(for: index),
                    score: mockQuestionScore(for: index),
                    analytics: mockQuestionAnalytics(for: index)
                )
            }
        }
    }
    
    // MARK: - Mock Data Helpers
    private func mockQuestionText(for index: Int) -> String {
        let questions = [
            "Tính đạo hàm của hàm số f(x) = x² + 2x + 1",
            "Giải phương trình bậc hai: x² - 5x + 6 = 0",
            "Số π có giá trị xấp xỉ bằng 3.14159",
            "Tìm giá trị lớn nhất của hàm số y = -x² + 4x - 3",
            "Tính tích phân ∫(2x + 1)dx từ 0 đến 2"
        ]
        return questions[(index - 1) % questions.count]
    }
    
    private func mockQuestionType(for index: Int) -> QuestionType {
        let types: [QuestionType] = [.singleChoice, .essay, .trueFalse, .multipleChoice]
        return types[(index - 1) % types.count]
    }
    
    private func mockQuestionScore(for index: Int) -> Double {
        return [10.0, 15.0, 5.0, 20.0, 25.0][(index - 1) % 5]
    }
    
    private func mockQuestionAnalytics(for index: Int) -> QuestionAnalytics? {
        guard viewModel.hasAnalytics else { return nil }
        
        let analytics = viewModel.getQuestionAnalyticsData()
        return analytics.indices.contains(index - 1) ? analytics[index - 1] : nil
    }
}

// MARK: - Supporting Views
struct StatItem: View {
    let label: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(AppConstants.Typography.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.primary)
            
            Text(label)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
}

struct QuestionDetailRow: View {
    let questionNumber: Int
    let questionText: String
    let questionType: QuestionType
    let score: Double
    let analytics: QuestionAnalytics?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Câu \(questionNumber)")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    HStack(spacing: 8) {
                        Image(systemName: questionType.icon)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(questionType.displayName)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(Int(score)) điểm")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    if let analytics = analytics {
                        let correctRate = Double(analytics.correctAnswers) / Double(analytics.totalAnswers) * 100
                        Text(String(format: "%.1f%% đúng", correctRate))
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(correctRateColor(correctRate))
                    }
                }
            }
            
            // Question Text
            Text(questionText)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(3)
            
            // Analytics Bar (if available)
            if let analytics = analytics {
                QuestionAnalyticsBar(analytics: analytics)
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    private func correctRateColor(_ rate: Double) -> Color {
        if rate >= 80 {
            return AppConstants.Colors.success
        } else if rate >= 60 {
            return AppConstants.Colors.warning
        } else {
            return AppConstants.Colors.error
        }
    }
}

struct QuestionAnalyticsBar: View {
    let analytics: QuestionAnalytics
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Phân tích:")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                DifficultyBadge(difficulty: analytics.difficulty)
            }
            
            // Correct vs Incorrect Bar
            GeometryReader { geometry in
                HStack(spacing: 2) {
                    // Correct answers
                    Rectangle()
                        .fill(AppConstants.Colors.success)
                        .frame(width: geometry.size.width * (Double(analytics.correctAnswers) / Double(analytics.totalAnswers)))
                    
                    // Incorrect answers
                    Rectangle()
                        .fill(AppConstants.Colors.error)
                        .frame(width: geometry.size.width * (Double(analytics.totalAnswers - analytics.correctAnswers) / Double(analytics.totalAnswers)))
                }
            }
            .frame(height: 6)
            .cornerRadius(3)
            
            HStack {
                HStack(spacing: 4) {
                    Circle()
                        .fill(AppConstants.Colors.success)
                        .frame(width: 8, height: 8)
                    
                    Text("\(analytics.correctAnswers) đúng")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                HStack(spacing: 4) {
                    Circle()
                        .fill(AppConstants.Colors.error)
                        .frame(width: 8, height: 8)
                    
                    Text("\(analytics.totalAnswers - analytics.correctAnswers) sai")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
    }
}

// MARK: - Preview
struct QuizQuestionsSection_Previews: PreviewProvider {
    static var previews: some View {
        QuizQuestionsSection(viewModel: QuizDetailViewModel(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 5,
            studentCount: 30,
            attemptCount: 25,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        )))
        .padding()
        .background(AppConstants.Colors.background)
    }
}
