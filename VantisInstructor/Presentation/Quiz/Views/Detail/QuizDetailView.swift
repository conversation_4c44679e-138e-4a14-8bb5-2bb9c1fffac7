//
//  QuizDetailView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct QuizDetailView: View {
    @StateObject private var viewModel: QuizDetailViewModel
    @Environment(\.dismiss) private var dismiss
    
    init(quiz: Quiz) {
        self._viewModel = StateObject(wrappedValue: QuizDetailViewModel(quiz: quiz))
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Tab Selector
                tabSelector
                
                // Content
                ScrollView {
                    VStack(spacing: AppConstants.UI.sectionSpacing) {
                        currentTabContent
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                    .padding(.top, AppConstants.UI.sectionSpacing) // Space from tab selector
                    .padding(.bottom, 100) // Space for floating action button
                }
                .refreshable {
                    await viewModel.refreshCurrentTab()
                }
            }
            .background(AppConstants.Colors.background)
            .navigationTitle(viewModel.quiz.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "chevron.left")
                            .font(.title3)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        ForEach(viewModel.availableActions, id: \.self) { action in
                            Button {
                                viewModel.performAction(action)
                            } label: {
                                Label(action.rawValue, systemImage: action.icon)
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title3)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .overlay(
                // Floating Action Button
                floatingActionButton,
                alignment: .bottomTrailing
            )
            .task {
                await viewModel.loadInitialData()
            }
            .alert("Xác nhận", isPresented: $viewModel.showingActionConfirmation) {
                Button("Hủy", role: .cancel) {
                    viewModel.showingActionConfirmation = false
                    viewModel.pendingAction = nil
                }
                
                Button(confirmButtonTitle, role: confirmButtonRole) {
                    Task {
                        let success = await viewModel.confirmAction()
                        if success && viewModel.pendingAction == .delete {
                            dismiss()
                        }
                    }
                }
            } message: {
                Text(confirmationMessage)
            }
            .alert("Lỗi", isPresented: $viewModel.showingError) {
                Button("OK") {
                    viewModel.showingError = false
                }
            } message: {
                Text(viewModel.errorMessage ?? "Đã xảy ra lỗi không xác định")
            }
            .sheet(isPresented: $viewModel.showingEditQuiz) {
                CreateQuizView()
                    .environmentObject(AuthViewModel()) // TODO: Pass proper auth view model
            }
            .sheet(isPresented: $viewModel.showingAssignment) {
                LessonAssignmentView(quiz: viewModel.quiz)
            }
        }
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(QuizDetailTab.allCases, id: \.self) { tab in
                Button {
                    viewModel.selectTab(tab)
                } label: {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                            .foregroundColor(tabColor(for: tab))
                        
                        Text(tab.rawValue)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(tabColor(for: tab))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        Rectangle()
                            .fill(viewModel.selectedTab == tab ? AppConstants.Colors.primary.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(AppConstants.Colors.cardBackground)
        .overlay(
            Rectangle()
                .fill(AppConstants.Colors.border)
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
    
    private func tabColor(for tab: QuizDetailTab) -> Color {
        return viewModel.selectedTab == tab ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary
    }
    
    // MARK: - Current Tab Content
    @ViewBuilder
    private var currentTabContent: some View {
        switch viewModel.selectedTab {
        case .overview:
            QuizOverviewSection(viewModel: viewModel)
        case .analytics:
            QuizAnalyticsSection(viewModel: viewModel)
        case .questions:
            QuizQuestionsSection(viewModel: viewModel)
        case .attempts:
            QuizAttemptsSection(viewModel: viewModel)
        }
    }
    
    // MARK: - Floating Action Button
    @ViewBuilder
    private var floatingActionButton: some View {
        if viewModel.canEdit {
            Button {
                viewModel.showingEditQuiz = true
            } label: {
                Image(systemName: "pencil")
                    .font(.title3)
                    .foregroundColor(.white)
                    .frame(width: 56, height: 56)
                    .background(AppConstants.Colors.primary)
                    .clipShape(Circle())
                    .shadow(
                        color: AppConstants.Colors.shadow.opacity(0.3),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
            }
            .padding(.trailing, AppConstants.UI.screenPadding)
            .padding(.bottom, 20)
        }
    }
    
    // MARK: - Confirmation Helpers
    private var confirmButtonTitle: String {
        guard let action = viewModel.pendingAction else { return "Xác nhận" }
        return action.rawValue
    }
    
    private var confirmButtonRole: ButtonRole? {
        guard let action = viewModel.pendingAction else { return nil }
        return action == .delete ? .destructive : nil
    }
    
    private var confirmationMessage: String {
        guard let action = viewModel.pendingAction else { return "" }
        
        switch action {
        case .edit:
            return "Mở chế độ chỉnh sửa quiz?"
        case .assign:
            return "Gán quiz vào bài học? Bạn có thể chọn bài học và cấu hình thời gian."
        case .markReady:
            return "Đánh dấu quiz này là sẵn sàng? Quiz sẽ có thể được phát hành sau khi sẵn sàng."
        case .publish:
            return "Phát hành quiz này? Học sinh sẽ có thể làm bài sau khi phát hành."
        case .unpublish:
            return "Hủy phát hành quiz này? Học sinh sẽ không thể làm bài nữa."
        case .archive:
            return "Lưu trữ quiz này? Quiz sẽ được chuyển vào kho lưu trữ."
        case .delete:
            return "Xóa vĩnh viễn quiz này? Hành động này không thể hoàn tác."
        }
    }
}

// MARK: - Preview
struct QuizDetailView_Previews: PreviewProvider {
    static var previews: some View {
        QuizDetailView(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 25,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        ))
    }
}
