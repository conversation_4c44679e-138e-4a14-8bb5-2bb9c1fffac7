//
//  ErrorModal.swift
//  VantisInstructor
//
//  Created by VantisInstructor Team on 31/7/25.
//

import SwiftUI

struct ErrorModal: View {
    let title: String
    let message: String
    let primaryButtonTitle: String
    let secondaryButtonTitle: String?
    let onPrimaryAction: () -> Void
    let onSecondaryAction: (() -> Void)?
    let onDismiss: () -> Void
    
    @State private var showContent = false
    @State private var showIcon = false
    @State private var pulseAnimation = false
    
    init(
        title: String = "Lỗi",
        message: String,
        primaryButtonTitle: String = "Thử lại",
        secondaryButtonTitle: String? = "Đóng",
        onPrimaryAction: @escaping () -> Void = {},
        onSecondaryAction: (() -> Void)? = nil,
        onDismiss: @escaping () -> Void
    ) {
        self.title = title
        self.message = message
        self.primaryButtonTitle = primaryButtonTitle
        self.secondaryButtonTitle = secondaryButtonTitle
        self.onPrimaryAction = onPrimaryAction
        self.onSecondaryAction = onSecondaryAction
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }

            // Modal content
            VStack(spacing: 0) {
                // Error icon
                errorIcon
                
                // Content
                VStack(spacing: 20) {
                    // Title
                    Text(title)
                        .font(.beVietnamPro(.bold, size: 20))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)

                    // Message
                    Text(message)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .lineSpacing(2)
                    
                    // Buttons
                    buttonStack
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 24)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showContent ? 1.0 : 0.8)
            .opacity(showContent ? 1.0 : 0.0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showContent = true
            }
            
            // Delay icon animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    showIcon = true
                }
            }
            
            // Start pulse animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    pulseAnimation = true
                }
            }
        }
    }
    
    // MARK: - Error Icon
    private var errorIcon: some View {
        ZStack {
            // Background circle with gradient
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.red.opacity(0.1),
                            Color.red.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .scaleEffect(pulseAnimation ? 1.1 : 1.0)
            
            // Error icon
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(.red)
                .scaleEffect(showIcon ? 1.0 : 0.5)
                .rotationEffect(.degrees(showIcon ? 0 : -180))
        }
        .padding(.top, 32)
        .padding(.bottom, 24)
    }
    
    // MARK: - Button Stack
    private var buttonStack: some View {
        VStack(spacing: 12) {
            // Primary button (Thử lại)
            Button(action: {
                onPrimaryAction()
                dismissModal()
            }) {
                Text(primaryButtonTitle)
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Secondary button (Đóng) - if provided
            if let secondaryTitle = secondaryButtonTitle {
                Button(action: {
                    onSecondaryAction?()
                    dismissModal()
                }) {
                    Text(secondaryTitle)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(AppConstants.Colors.surfaceSecondary)
                        .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Helper Methods
    private func dismissModal() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showContent = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onDismiss()
        }
    }
}

// MARK: - Preview
#Preview {
    ErrorModal(
        title: "Lỗi kết nối",
        message: "Không thể kết nối đến server. Vui lòng kiểm tra kết nối internet và thử lại.",
        primaryButtonTitle: "Thử lại",
        secondaryButtonTitle: "Đóng",
        onPrimaryAction: {
            print("Retry tapped")
        },
        onSecondaryAction: {
            print("Close tapped")
        },
        onDismiss: {
            print("Modal dismissed")
        }
    )
}
