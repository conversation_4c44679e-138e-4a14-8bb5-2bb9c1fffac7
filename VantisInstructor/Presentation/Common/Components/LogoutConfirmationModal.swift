//
//  LogoutConfirmationModal.swift
//  VantisInstructor
//
//  Created by LinkX Team on 31/7/25.
//

import SwiftUI

struct LogoutConfirmationModal: View {
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    @State private var isVisible = false
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }
            
            // Modal content
            VStack(spacing: 0) {
                // Warning icon
                warningIcon
                
                // Content
                VStack(spacing: 20) {
                    // Title
                    Text("Đăng xuất")
                        .font(.beVietnamPro(.bold, size: 22))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                    
                    // Message
                    Text("Bạn có chắc chắn muốn đăng xuất không?")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .lineSpacing(2)
                    
                    // Buttons
                    buttonStack
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 24)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 32)
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isVisible)
        }
        .onAppear {
            withAnimation {
                isVisible = true
            }
        }
    }
    
    // MARK: - Warning Icon
    private var warningIcon: some View {
        ZStack {
            Circle()
                .fill(AppConstants.Colors.warning.opacity(0.1))
                .frame(width: 80, height: 80)
            
            Circle()
                .fill(AppConstants.Colors.warning.opacity(0.2))
                .frame(width: 60, height: 60)
            
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 28, weight: .medium))
                .foregroundColor(AppConstants.Colors.warning)
        }
        .padding(.top, 32)
        .padding(.bottom, 24)
    }
    
    // MARK: - Button Stack
    private var buttonStack: some View {
        VStack(spacing: 12) {
            // Confirm button (Đăng xuất)
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isVisible = false
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onConfirm()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("Đăng xuất")
                        .font(.beVietnamPro(.semiBold, size: 16))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 52)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppConstants.Colors.error, Color.red.opacity(0.8)]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
                .shadow(color: AppConstants.Colors.error.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Cancel button (Hủy)
            Button(action: dismissModal) {
                Text("Hủy")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 52)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppConstants.Colors.surfaceSecondary)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(AppConstants.Colors.border, lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Helper Methods
    private func dismissModal() {
        withAnimation(.easeInOut(duration: 0.2)) {
            isVisible = false
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            onCancel()
        }
    }
}

// MARK: - Preview
#Preview {
    LogoutConfirmationModal(
        onConfirm: {
            print("Confirmed logout")
        },
        onCancel: {
            print("Cancelled logout")
        }
    )
}
