//
//  APILessonCard.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 28/7/25.
//

import SwiftUI

struct APILessonCard: View {
    let lesson: APILesson
    let onTap: () -> Void
    @State private var showingLessonDetail = false
    
    var body: some View {
        Button(action: {
            showingLessonDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with thumbnail and course info
                HStack(alignment: .top, spacing: 12) {
                    // Thumbnail icon
                    LessonThumbnailIcon()

                    // Course info
                    VStack(alignment: .leading, spacing: 8) {
                        Text(lesson.classCode)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text(lesson.name)
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        // Status badge below title
                        HStack {
                            StatusBadge(
                                text: lesson.statusBadgeText,
                                color: lesson.statusBadgeColor,
                                isToday: lesson.isToday,
                                needsAttention: lesson.needsAttention
                            )

                            Spacer()
                        }
                    }
                }

                // Date, time and location info - aligned with title
                HStack(alignment: .top, spacing: 12) {
                    // Empty space to align with title (same width as thumbnail + spacing)
                    Spacer()
                        .frame(width: 48 + 12)

                    VStack(spacing: 8) {
                    // Date and time info (same line)
                    HStack(spacing: 16) {
                        // Date
                        HStack(spacing: 6) {
                            Image(systemName: "calendar")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(lesson.formattedDate)
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }

                        // Time
                        HStack(spacing: 6) {
                            Image(systemName: "clock")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(lesson.timeRange)
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }

                        Spacer()
                    }

                    // Location info (separate line)
                    HStack(spacing: 6) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text("Phòng \(lesson.room)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Spacer()
                    }
                    }
                }

                // Attendance info (only for completed lessons) - aligned with title
                if lesson.state == .completed {
                    HStack(alignment: .top, spacing: 12) {
                        // Empty space to align with title
                        Spacer()
                            .frame(width: 48 + 12)

                        attendanceInfoView
                    }
                }
                

            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLessonDetail) {
            LessonDetailBottomSheet(lesson: lesson)
        }
    }
    
    // MARK: - Attendance Info View
    private var attendanceInfoView: some View {
        HStack(spacing: 12) {
            // Present count
            HStack(spacing: 4) {
                Image(systemName: "person.fill")
                    .font(.caption2)
                    .foregroundColor(.green)
                
                Text("\(lesson.presentCount)/\(lesson.totalStudents)")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Attendance rate
            HStack(spacing: 4) {
                Image(systemName: "chart.bar.fill")
                    .font(.caption2)
                    .foregroundColor(attendanceRateColor)
                
                Text("\(Int(lesson.attendanceRate))%")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(attendanceRateColor)
                    .fontWeight(.medium)
            }
            
            Spacer()
        }
    }
    

    
    // MARK: - Computed Properties
    private var attendanceRateColor: Color {
        if lesson.attendanceRate >= 90 {
            return .green
        } else if lesson.attendanceRate >= 70 {
            return .blue
        } else if lesson.attendanceRate >= 50 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Status Badge
struct StatusBadge: View {
    let text: String
    let color: String
    let isToday: Bool
    let needsAttention: Bool
    
    var body: some View {
        Text(text)
            .font(.beVietnamPro(.medium, size: 12))
            .fontWeight(.medium)
            .foregroundColor(badgeTextColor)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                CustomRoundedRectangle(
                    topLeading: 12,
                    topTrailing: 12,
                    bottomLeading: 0,
                    bottomTrailing: 12
                )
                .fill(badgeBackgroundColor)
            )
    }
    
    private var badgeTextColor: Color {
        if isToday {
            return .white
        } else {
            switch color {
            case "blue": return Color(red: 0.2, green: 0.4, blue: 0.9)
            case "green": return Color(red: 0.1, green: 0.7, blue: 0.3)
            case "orange": return Color(red: 0.9, green: 0.5, blue: 0.1)
            case "red": return Color(red: 0.9, green: 0.2, blue: 0.2)
            default: return Color(red: 0.5, green: 0.5, blue: 0.5)
            }
        }
    }

    private var badgeBackgroundColor: Color {
        if isToday {
            return Color(red: 0.1, green: 0.7, blue: 0.3)
        } else {
            switch color {
            case "blue": return Color(red: 0.2, green: 0.4, blue: 0.9).opacity(0.12)
            case "green": return Color(red: 0.1, green: 0.7, blue: 0.3).opacity(0.12)
            case "orange": return Color(red: 0.9, green: 0.5, blue: 0.1).opacity(0.12)
            case "red": return Color(red: 0.9, green: 0.2, blue: 0.2).opacity(0.12)
            default: return Color(red: 0.5, green: 0.5, blue: 0.5).opacity(0.12)
            }
        }
    }
}

// MARK: - Preview
struct APILessonCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // Today lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[1], // Today lesson
                onTap: {}
            )
            
            // Completed lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[0], // Completed lesson
                onTap: {}
            )
            
            // Upcoming lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[2], // Upcoming lesson
                onTap: {}
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}

// MARK: - Custom Rounded Rectangle
struct CustomRoundedRectangle: Shape {
    let topLeading: CGFloat
    let topTrailing: CGFloat
    let bottomLeading: CGFloat
    let bottomTrailing: CGFloat

    func path(in rect: CGRect) -> Path {
        var path = Path()

        let width = rect.size.width
        let height = rect.size.height

        // Start from top-left corner
        path.move(to: CGPoint(x: 0, y: topLeading))

        // Top-left corner
        if topLeading > 0 {
            path.addArc(center: CGPoint(x: topLeading, y: topLeading),
                       radius: topLeading,
                       startAngle: Angle(degrees: 180),
                       endAngle: Angle(degrees: 270),
                       clockwise: false)
        }

        // Top edge
        path.addLine(to: CGPoint(x: width - topTrailing, y: 0))

        // Top-right corner
        if topTrailing > 0 {
            path.addArc(center: CGPoint(x: width - topTrailing, y: topTrailing),
                       radius: topTrailing,
                       startAngle: Angle(degrees: 270),
                       endAngle: Angle(degrees: 0),
                       clockwise: false)
        }

        // Right edge
        path.addLine(to: CGPoint(x: width, y: height - bottomTrailing))

        // Bottom-right corner
        if bottomTrailing > 0 {
            path.addArc(center: CGPoint(x: width - bottomTrailing, y: height - bottomTrailing),
                       radius: bottomTrailing,
                       startAngle: Angle(degrees: 0),
                       endAngle: Angle(degrees: 90),
                       clockwise: false)
        }

        // Bottom edge
        path.addLine(to: CGPoint(x: bottomLeading, y: height))

        // Bottom-left corner (sharp corner when radius = 0)
        if bottomLeading > 0 {
            path.addArc(center: CGPoint(x: bottomLeading, y: height - bottomLeading),
                       radius: bottomLeading,
                       startAngle: Angle(degrees: 90),
                       endAngle: Angle(degrees: 180),
                       clockwise: false)
        }

        // Left edge
        path.addLine(to: CGPoint(x: 0, y: topLeading))

        return path
    }
}

// MARK: - Lesson Thumbnail Icon
struct LessonThumbnailIcon: View {
    var body: some View {
        ZStack {
            // Outer circle (light blue background)
            Circle()
                .fill(Color(red: 0.9, green: 0.95, blue: 1.0))
                .frame(width: 48, height: 48)

            // Inner circle (blue background)
            Circle()
                .fill(Color(red: 0.2, green: 0.6, blue: 1.0))
                .frame(width: 32, height: 32)

            // Book icon (white) - represents class/lesson
            Image(systemName: "book.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
        }
    }
}
