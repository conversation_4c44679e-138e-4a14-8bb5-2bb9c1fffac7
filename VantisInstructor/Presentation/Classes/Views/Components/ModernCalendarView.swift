//
//  ModernCalendarView.swift
//  VantisInstructor
//
//  Created by Instructor App on 30/7/25.
//

import SwiftUI

struct ModernCalendarView: View {
    @Binding var selectedDate: Date
    @State private var currentMonth: Date = Date()
    
    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }()
    
    private let dayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()
    
    private let weekdayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }()
    
    var body: some View {
        VStack(spacing: 20) {
            // Header with month/year and navigation
            headerView
            
            // Weekday labels
            weekdayLabels
            
            // Calendar grid
            calendarGrid
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                )
        )
        .onAppear {
            currentMonth = selectedDate
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Button(action: previousMonth) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(AppConstants.Colors.primary.opacity(0.1))
                    )
            }
            
            Spacer()
            
            Text(dateFormatter.string(from: currentMonth))
                .font(AppConstants.Typography.title2)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            Button(action: nextMonth) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(AppConstants.Colors.primary.opacity(0.1))
                    )
            }
        }
    }
    
    // MARK: - Weekday Labels
    private var weekdayLabels: some View {
        HStack(spacing: 0) {
            ForEach(weekdaySymbols, id: \.self) { weekday in
                Text(weekday)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 4)
    }
    
    // MARK: - Calendar Grid
    private var calendarGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
            ForEach(daysInMonth, id: \.self) { date in
                if let date = date {
                    dayCell(for: date)
                } else {
                    // Empty cell for padding
                    Color.clear
                        .frame(height: 44)
                }
            }
        }
    }
    
    // MARK: - Day Cell
    private func dayCell(for date: Date) -> some View {
        let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
        let isToday = calendar.isDate(date, inSameDayAs: Date())
        let isCurrentMonth = calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)

        return Button(action: {
            selectedDate = date
        }) {
            Text(dayFormatter.string(from: date))
                .font(isSelected ? AppConstants.Typography.subheadline : AppConstants.Typography.callout)
                .foregroundColor(dayTextColor(isSelected: isSelected, isToday: isToday, isCurrentMonth: isCurrentMonth))
                .frame(width: 44, height: 44)
                .background(
                    dayBackground(isSelected: isSelected, isToday: isToday)
                )
                .overlay(
                    // Today indicator (small dot)
                    isToday && !isSelected ?
                    Circle()
                        .fill(AppConstants.Colors.primary)
                        .frame(width: 6, height: 6)
                        .offset(y: 18)
                    : nil
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    private func dayTextColor(isSelected: Bool, isToday: Bool, isCurrentMonth: Bool) -> Color {
        if isSelected {
            return .white
        } else if !isCurrentMonth {
            return AppConstants.Colors.textSecondary.opacity(0.4)
        } else {
            return AppConstants.Colors.textPrimary
        }
    }
    
    private func dayBackground(isSelected: Bool, isToday: Bool) -> some View {
        Group {
            if isSelected {
                Circle()
                    .fill(AppConstants.Colors.primary)
            } else {
                Circle()
                    .fill(Color.clear)
            }
        }
    }
    
    private func previousMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
        }
    }
    
    private func nextMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
        }
    }
    
    // MARK: - Computed Properties
    private var weekdaySymbols: [String] {
        let symbols = calendar.veryShortWeekdaySymbols
        // Reorder to start with Monday (Vietnamese style)
        return Array(symbols[1...]) + [symbols[0]]
    }
    
    private var daysInMonth: [Date?] {
        guard let firstOfMonth = calendar.dateInterval(of: .month, for: currentMonth)?.start else {
            return []
        }
        
        let firstOfMonthWeekday = calendar.component(.weekday, from: firstOfMonth)
        // Adjust for Monday start (weekday 2 = Monday in Calendar)
        let adjustedWeekday = (firstOfMonthWeekday == 1) ? 7 : firstOfMonthWeekday - 1
        
        var days: [Date?] = Array(repeating: nil, count: adjustedWeekday - 1)
        
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0
        
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                days.append(date)
            }
        }
        
        // Fill remaining cells to complete the grid (6 rows × 7 days = 42 cells)
        while days.count < 42 {
            days.append(nil)
        }
        
        return days
    }
}

// MARK: - Preview
struct ModernCalendarView_Previews: PreviewProvider {
    static var previews: some View {
        ModernCalendarView(selectedDate: .constant(Date()))
            .padding()
            .background(AppConstants.Colors.background)
    }
}
